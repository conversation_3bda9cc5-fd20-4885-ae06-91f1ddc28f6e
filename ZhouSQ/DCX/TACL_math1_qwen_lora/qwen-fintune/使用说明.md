# 数学分类系统 - 使用说明

## 🎉 项目重构完成！

您的项目已经完全重构，现在拥有一个**简洁、易用、功能强大**的数学分类系统。

## 🔥 核心特性

### 1. **智能阈值策略**
- **相似度 ≥ 阈值**: 使用向量检索（快速匹配）
- **相似度 < 阈值**: 使用LoRA微调模型（精准分类）
- **默认阈值**: 0.8（可在config.py中调整）

### 2. **一键式操作**
- 构建向量知识库：`python main.py build`
- 训练LoRA模型：`python main.py train`
- 系统评估：`python main.py evaluate`
- 预测分类：`python main.py predict --query "你的问题"`

### 3. **完全自动化**
- 自动缓存向量，避免重复计算
- 自动选择最佳策略
- 自动保存训练好的模型

## 🚀 快速开始

### 方法1: 一键启动（推荐）
```bash
cd /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/qwen-fintune
python quick_start.py
```
这个脚本会自动完成所有初始化工作！

### 方法2: 分步操作
```bash
# 1. 检查系统状态
python main.py status

# 2. 构建向量知识库
python main.py build

# 3. 训练LoRA模型
python main.py train

# 4. 评估系统性能
python main.py evaluate

# 5. 开始预测
python main.py interactive
```

## 📊 系统架构

```
输入问题 → 向量检索 → 计算相似度
              ↓
      相似度 ≥ 0.8？
         ↙      ↘
       是         否
       ↓          ↓
   返回检索结果  LoRA模型分类
                  ↓
              返回分类结果
```

## 🎯 使用示例

### 交互式预测
```bash
python main.py interactive
```

### 单次预测
```bash
python main.py predict --query "小明有5个苹果，吃了2个，还剩几个？"
```

### 批量预测
```python
from math_classification_system import MathClassificationSystem

system = MathClassificationSystem()
queries = ["问题1", "问题2", "问题3"]
results = system.batch_predict(queries)
```

## ⚙️ 配置调整

在 `math_classification_system/config.py` 中：

```python
# 核心阈值配置
SIMILARITY_THRESHOLD = 0.8  # 调整这个值来改变策略选择

# 阈值建议：
# 0.9+ : 更多使用LoRA模型（精度高，速度慢）
# 0.7- : 更多使用向量检索（速度快，精度略低）
```

## 📁 新项目结构

```
qwen-fintune/
├── main.py                    # 主程序入口 ⭐
├── quick_start.py            # 一键启动脚本 ⭐
├── example.py                # 使用示例 ⭐
├── README.md                 # 详细文档 ⭐
├── requirements.txt          # 依赖包 ⭐
├── math_classification_system/  # 核心系统 ⭐
│   ├── config.py            # 配置文件
│   ├── core_system.py       # 核心系统类
│   ├── vector_builder.py    # 向量构建
│   ├── model_trainer.py     # 模型微调
│   ├── model_evaluator.py   # 模型评估
│   └── predictor.py         # 预测生成
├── data/                    # 数据目录
└── outputs/                 # 输出目录
    ├── cache/              # 向量缓存
    ├── models/             # 训练模型
    └── results/            # 评估结果
```

## 🔧 与旧版本的区别

### 旧版本问题：
- ❌ 代码分散，难以理解
- ❌ 重复代码多
- ❌ 没有清晰的入口
- ❌ 配置复杂

### 新版本优势：
- ✅ 统一的系统入口
- ✅ 清晰的模块划分
- ✅ 简单的命令行界面
- ✅ 智能的阈值策略
- ✅ 完整的文档和示例

## 💡 使用技巧

1. **首次使用**：运行 `python quick_start.py`
2. **日常使用**：运行 `python main.py interactive`
3. **调整阈值**：修改 `config.py` 中的 `SIMILARITY_THRESHOLD`
4. **查看详情**：使用 `--verbose` 参数
5. **批量处理**：使用 `system.batch_predict()`

## 🐛 常见问题

**Q: 提示模型路径不存在？**
A: 检查 `config.py` 中的路径配置

**Q: 显存不足？**
A: 减小 `TRAIN_BATCH_SIZE` 或使用CPU

**Q: 预测效果不好？**
A: 调整 `SIMILARITY_THRESHOLD` 阈值

## 🎊 总结

现在您有了一个**完全重构、简洁易用**的数学分类系统：

1. **功能完整**：向量构建、模型微调、模型评估、预测生成
2. **使用简单**：一行命令即可完成所有操作
3. **智能策略**：根据阈值自动选择最佳方法
4. **文档完善**：详细的使用说明和示例

**立即开始使用：**
```bash
cd /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/qwen-fintune
python quick_start.py
```

🎉 **享受您的新系统吧！**
