# 数学分类系统

基于阈值的智能数学问题分类系统，结合向量检索和LoRA微调的混合方案。

## 🎯 核心特性

- **智能阈值策略**: 根据相似度阈值自动选择最佳分类方法
- **向量检索**: 高相似度时使用快速向量匹配
- **LoRA微调**: 低相似度时使用精准的微调模型
- **简单易用**: 一键式命令行界面
- **高效缓存**: 支持向量缓存和增量更新

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install torch transformers numpy scikit-learn tqdm datasets accelerate faiss-cpu pandas peft
```

### 2. 检查系统状态

```bash
python main.py status
```

### 3. 构建向量知识库

```bash
python main.py build
```

### 4. 训练LoRA微调模型

```bash
python main.py train
```

### 5. 评估系统性能

```bash
python main.py evaluate
```

### 6. 开始预测

```bash
# 单次预测
python main.py predict --query "小明有5个苹果，吃了2个，还剩几个？"

# 交互式预测
python main.py interactive
```

## 📊 工作原理

### 核心算法流程

```
输入查询 → 向量检索 → 计算相似度
                ↓
        相似度 >= 阈值？
           ↙        ↘
        是            否
        ↓             ↓
    返回检索结果    LoRA模型分类
                      ↓
                  返回分类结果
```

### 阈值配置

在 `math_classification_system/config.py` 中修改：

```python
# 核心阈值配置 - 这是您最关心的部分
SIMILARITY_THRESHOLD = 0.8  # 高于此阈值使用向量检索，低于此阈值使用LoRA微调模型
```

## 🛠️ 命令详解

### 构建向量知识库
```bash
python main.py build [选项]

选项:
  --no-cache    不使用缓存，重新计算所有向量
  --force       强制重建，忽略现有缓存
```

### 训练模型
```bash
python main.py train [选项]

选项:
  --force       强制重新训练，忽略已有模型
```

### 评估系统
```bash
python main.py evaluate
```
输出示例：
```
📈 评估结果:
----------------------------------------
向量检索    : 0.856 (1234/1441)
模型分类    : 0.892 (1285/1441)
混合系统    : 0.901 (1298/1441)
  策略使用: 向量检索 892 次, 模型分类 549 次
```

### 单次预测
```bash
python main.py predict --query "你的问题" [选项]

选项:
  --verbose     显示详细预测信息
```

### 交互式预测
```bash
python main.py interactive
```

交互式命令：
- 直接输入问题进行预测
- `explain <问题>` - 查看详细预测过程
- `help` - 查看帮助
- `quit` - 退出程序

## 📁 项目结构

```
qwen-fintune/
├── main.py                           # 主程序入口
├── math_classification_system/       # 核心系统模块
│   ├── __init__.py
│   ├── config.py                     # 配置文件
│   ├── core_system.py               # 核心系统类
│   ├── vector_builder.py            # 向量构建模块
│   ├── model_trainer.py             # 模型微调模块
│   ├── model_evaluator.py           # 模型评估模块
│   └── predictor.py                 # 预测生成模块
├── data/                            # 数据目录
│   ├── train.json
│   ├── test.json
│   └── val.json
└── outputs/                         # 输出目录
    ├── cache/                       # 向量缓存
    ├── models/                      # 训练好的模型
    └── results/                     # 评估结果
```

## ⚙️ 配置说明

### 核心配置 (config.py)

```python
# 阈值配置
SIMILARITY_THRESHOLD = 0.8    # 相似度阈值

# 模型路径
EMBEDDING_MODEL_PATH = "/path/to/Qwen3-Embedding-4B"
CLASSIFICATION_MODEL_PATH = "/path/to/Qwen3-4B"

# LoRA配置
USE_LORA = True
LORA_R = 16
LORA_ALPHA = 32
LORA_DROPOUT = 0.1

# 训练配置
TRAIN_EPOCHS = 3
TRAIN_BATCH_SIZE = 8
LEARNING_RATE = 2e-5
```

## 🔧 高级用法

### 1. 调整阈值

根据您的需求调整相似度阈值：
- **阈值较高 (0.9+)**: 更多使用LoRA模型，精度更高但速度较慢
- **阈值较低 (0.7-)**: 更多使用向量检索，速度更快但可能精度略低

### 2. 批量预测

```python
from math_classification_system import MathClassificationSystem

system = MathClassificationSystem()
queries = ["问题1", "问题2", "问题3"]
results = system.batch_predict(queries)
```

### 3. 详细预测解释

```python
explanation = system.predictor.explain_prediction(
    "你的问题", system.vector_builder, system.model_trainer
)
print(explanation)
```

## 📈 性能优化

### 1. GPU加速
确保CUDA可用以获得最佳性能：
```bash
nvidia-smi  # 检查GPU状态
```

### 2. 缓存管理
- 向量缓存自动保存在 `outputs/cache/` 目录
- 首次构建后，后续启动会自动加载缓存
- 使用 `--no-cache` 强制重新计算

### 3. 内存优化
- 调整 `EMBEDDING_BATCH_SIZE` 控制内存使用
- 使用LoRA微调减少显存占用

## 🐛 常见问题

### Q: 提示模型路径不存在？
A: 请检查 `config.py` 中的模型路径是否正确，确保模型文件已下载。

### Q: 训练过程中显存不足？
A: 减小 `TRAIN_BATCH_SIZE` 或启用梯度累积。

### Q: 预测结果不理想？
A: 尝试调整 `SIMILARITY_THRESHOLD` 或重新训练模型。

### Q: 向量构建很慢？
A: 确保使用GPU加速，检查 `DEVICE` 配置。

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持基于阈值的混合分类策略
- 提供完整的命令行界面
- 支持向量缓存和模型训练

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
