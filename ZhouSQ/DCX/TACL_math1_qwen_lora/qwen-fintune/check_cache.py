#!/usr/bin/env python3
"""
缓存检查脚本 - 检查现有的向量缓存和模型文件
"""

import sys
from pathlib import Path
import json
import pickle

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from math_classification_system import Config

def check_vector_cache():
    """检查向量缓存"""
    print("🔍 检查向量缓存...")
    print("-" * 50)
    
    cache_info = Config.check_existing_cache()
    
    if cache_info['vector_cache_exists']:
        cache_path = cache_info['vector_cache_path']
        print(f"✅ 发现向量缓存: {cache_path}")
        
        try:
            # 读取缓存信息
            with open(cache_path, 'rb') as f:
                cache_data = pickle.load(f)
            
            if 'texts' in cache_data and 'vectors' in cache_data:
                # 新版本格式
                num_texts = len(cache_data['texts'])
                vector_shape = cache_data['vectors'].shape if hasattr(cache_data['vectors'], 'shape') else 'Unknown'
                print(f"   📊 文本数量: {num_texts}")
                print(f"   📐 向量维度: {vector_shape}")
                print(f"   📝 格式: 新版本")
                
                if 'config_hash' in cache_data:
                    print(f"   🔑 配置哈希: {cache_data['config_hash']}")
                
            elif 'embeddings' in cache_data and 'texts' in cache_data:
                # 旧版本格式
                num_texts = len(cache_data['texts'])
                vector_shape = cache_data['embeddings'].shape if hasattr(cache_data['embeddings'], 'shape') else 'Unknown'
                print(f"   📊 文本数量: {num_texts}")
                print(f"   📐 向量维度: {vector_shape}")
                print(f"   📝 格式: 旧版本 (来自similarity_matching_project)")
                print(f"   💡 提示: 系统会自动转换为新格式")
                
            else:
                print(f"   ⚠️  未知的缓存格式")
                
        except Exception as e:
            print(f"   ❌ 读取缓存失败: {e}")
    else:
        print("❌ 未发现向量缓存")
        print("   💡 需要运行: python main.py build")

def check_model_cache():
    """检查模型缓存"""
    print("\n🤖 检查模型缓存...")
    print("-" * 50)
    
    cache_info = Config.check_existing_cache()
    
    if cache_info['model_cache_exists']:
        model_path = cache_info['model_cache_path']
        print(f"✅ 发现模型缓存: {model_path}")
        
        # 检查模型文件
        adapter_config_path = model_path / "adapter_config.json"
        adapter_model_path = model_path / "adapter_model.safetensors"
        tokenizer_config_path = model_path / "tokenizer_config.json"
        label_encoder_path = model_path / "label_encoder.json"
        
        print(f"   📁 模型目录: {model_path}")
        print(f"   🔧 LoRA配置: {'✅' if adapter_config_path.exists() else '❌'}")
        print(f"   🧠 LoRA权重: {'✅' if adapter_model_path.exists() else '❌'}")
        print(f"   📝 分词器: {'✅' if tokenizer_config_path.exists() else '❌'}")
        print(f"   🏷️  标签编码器: {'✅' if label_encoder_path.exists() else '❌'}")
        
        # 读取LoRA配置
        if adapter_config_path.exists():
            try:
                with open(adapter_config_path, 'r') as f:
                    adapter_config = json.load(f)
                
                print(f"   📊 LoRA rank: {adapter_config.get('r', 'Unknown')}")
                print(f"   📈 LoRA alpha: {adapter_config.get('lora_alpha', 'Unknown')}")
                print(f"   🎯 目标模块: {adapter_config.get('target_modules', 'Unknown')}")
                
            except Exception as e:
                print(f"   ⚠️  读取LoRA配置失败: {e}")
        
        # 读取标签编码器
        if label_encoder_path.exists():
            try:
                with open(label_encoder_path, 'r', encoding='utf-8') as f:
                    label_mapping = json.load(f)
                
                num_labels = len(label_mapping.get('classes_', []))
                print(f"   🏷️  标签数量: {num_labels}")
                
                # 显示前几个标签作为示例
                if num_labels > 0:
                    sample_labels = label_mapping['classes_'][:3]
                    print(f"   📋 标签示例: {sample_labels}")
                
            except Exception as e:
                print(f"   ⚠️  读取标签编码器失败: {e}")
        else:
            print(f"   ⚠️  未找到标签编码器，系统会从训练数据重新创建")
            
    else:
        print("❌ 未发现模型缓存")
        print("   💡 需要运行: python main.py train")

def check_system_compatibility():
    """检查系统兼容性"""
    print("\n🔧 检查系统兼容性...")
    print("-" * 50)
    
    # 检查路径配置
    errors = Config.validate_paths()
    if errors:
        print("❌ 路径配置问题:")
        for error in errors:
            print(f"   • {error}")
    else:
        print("✅ 路径配置正常")
    
    # 检查当前阈值设置
    print(f"📊 当前相似度阈值: {Config.SIMILARITY_THRESHOLD}")
    print(f"🔧 LoRA微调: {'启用' if Config.USE_LORA else '禁用'}")
    print(f"💻 计算设备: {Config.DEVICE}")

def provide_recommendations():
    """提供使用建议"""
    print("\n💡 使用建议...")
    print("-" * 50)
    
    cache_info = Config.check_existing_cache()
    
    if cache_info['vector_cache_exists'] and cache_info['model_cache_exists']:
        print("🎉 您的系统已经准备就绪！")
        print("   可以直接开始预测:")
        print("   • python main.py interactive")
        print("   • python main.py predict --query '你的问题'")
        print("   • python main.py evaluate")
        
    elif cache_info['vector_cache_exists'] and not cache_info['model_cache_exists']:
        print("📊 向量缓存已存在，但需要训练模型:")
        print("   • python main.py train")
        print("   然后就可以开始预测了")
        
    elif not cache_info['vector_cache_exists'] and cache_info['model_cache_exists']:
        print("🤖 模型已存在，但需要构建向量缓存:")
        print("   • python main.py build")
        print("   然后就可以开始预测了")
        
    else:
        print("🚀 需要完整初始化系统:")
        print("   • python quick_start.py  (推荐，一键完成)")
        print("   或者分步执行:")
        print("   • python main.py build")
        print("   • python main.py train")
    
    print("\n🔧 如果想要重新构建:")
    print("   • python main.py build --force    (重新构建向量)")
    print("   • python main.py train --force    (重新训练模型)")

def main():
    """主函数"""
    print("🧮 数学分类系统 - 缓存检查")
    print("=" * 60)
    
    try:
        # 检查向量缓存
        check_vector_cache()
        
        # 检查模型缓存
        check_model_cache()
        
        # 检查系统兼容性
        check_system_compatibility()
        
        # 提供使用建议
        provide_recommendations()
        
        print("\n" + "=" * 60)
        print("✅ 缓存检查完成")
        
    except Exception as e:
        print(f"\n❌ 检查过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
