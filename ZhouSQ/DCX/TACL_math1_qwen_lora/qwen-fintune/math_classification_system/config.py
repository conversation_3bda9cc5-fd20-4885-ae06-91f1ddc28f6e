"""
配置文件 - 简化的系统配置
"""

import os
from pathlib import Path

class Config:
    """系统配置类"""
    
    # 基础路径配置
    PROJECT_ROOT = Path(__file__).parent.parent
    DATA_ROOT = "/home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora"
    
    # 模型路径
    EMBEDDING_MODEL_PATH = f"{DATA_ROOT}/Qwen3-Embedding-4B"
    CLASSIFICATION_MODEL_PATH = f"{DATA_ROOT}/Qwen3-4B"
    
    # 数据路径
    DATA_DIR = f"{DATA_ROOT}/qwen-fintune/data"
    TRAIN_DATA_PATH = f"{DATA_DIR}/train.json"
    TEST_DATA_PATH = f"{DATA_DIR}/test.json"
    VAL_DATA_PATH = f"{DATA_DIR}/val.json"
    
    # 输出路径
    OUTPUT_DIR = PROJECT_ROOT / "outputs"
    CACHE_DIR = OUTPUT_DIR / "cache"
    MODELS_DIR = OUTPUT_DIR / "models"
    RESULTS_DIR = OUTPUT_DIR / "results"

    # 兼容旧版本的缓存和模型路径
    LEGACY_CACHE_DIR = OUTPUT_DIR / "cache"
    LEGACY_MODELS_DIR = OUTPUT_DIR / "models"
    
    # 核心阈值配置 - 这是您最关心的部分
    SIMILARITY_THRESHOLD = 0.8  # 高于此阈值使用向量检索，低于此阈值使用LoRA微调模型
    
    # 设备配置
    DEVICE = "cuda" if os.system("nvidia-smi > /dev/null 2>&1") == 0 else "cpu"
    
    # 向量检索配置
    EMBEDDING_BATCH_SIZE = 32
    TOP_K_SIMILAR = 5
    USE_FAISS = True
    
    # LoRA微调配置
    USE_LORA = True
    LORA_R = 16
    LORA_ALPHA = 32
    LORA_DROPOUT = 0.1
    LORA_TARGET_MODULES = ["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    
    # 训练配置
    TRAIN_EPOCHS = 10
    TRAIN_BATCH_SIZE = 2  # 减少批次大小以节省内存
    LEARNING_RATE = 2e-5
    MAX_LENGTH = 512  # 减少最大长度以节省内存
    
    @classmethod
    def init_directories(cls):
        """初始化必要的目录"""
        for directory in [cls.OUTPUT_DIR, cls.CACHE_DIR, cls.MODELS_DIR, cls.RESULTS_DIR]:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def validate_paths(cls):
        """验证关键路径是否存在"""
        errors = []

        if not os.path.exists(cls.EMBEDDING_MODEL_PATH):
            errors.append(f"嵌入模型路径不存在: {cls.EMBEDDING_MODEL_PATH}")

        if not os.path.exists(cls.CLASSIFICATION_MODEL_PATH):
            errors.append(f"分类模型路径不存在: {cls.CLASSIFICATION_MODEL_PATH}")

        if not os.path.exists(cls.TRAIN_DATA_PATH):
            errors.append(f"训练数据路径不存在: {cls.TRAIN_DATA_PATH}")

        return errors

    @classmethod
    def check_existing_cache(cls):
        """检查是否存在可用的缓存文件"""
        cache_info = {
            'vector_cache_exists': False,
            'vector_cache_path': None,
            'model_cache_exists': False,
            'model_cache_path': None
        }

        # 检查向量缓存 - 支持多种可能的文件名
        possible_vector_caches = [
            cls.CACHE_DIR / "vectors.pkl",
            cls.CACHE_DIR / "train_embeddings.pkl",  # 旧版本文件名
            cls.LEGACY_CACHE_DIR / "train_embeddings.pkl"
        ]

        for cache_path in possible_vector_caches:
            if cache_path.exists():
                cache_info['vector_cache_exists'] = True
                cache_info['vector_cache_path'] = cache_path
                break

        # 检查模型缓存 - 查找LoRA checkpoint
        possible_model_dirs = [
            cls.MODELS_DIR / "classification_model",
            cls.LEGACY_MODELS_DIR
        ]

        for model_dir in possible_model_dirs:
            if model_dir.exists():
                # 查找checkpoint目录
                if (model_dir / "adapter_config.json").exists():
                    # 直接是模型目录
                    cache_info['model_cache_exists'] = True
                    cache_info['model_cache_path'] = model_dir
                    break
                else:
                    # 查找子目录中的checkpoint
                    for subdir in model_dir.iterdir():
                        if subdir.is_dir() and (subdir / "adapter_config.json").exists():
                            cache_info['model_cache_exists'] = True
                            cache_info['model_cache_path'] = subdir
                            break
                    if cache_info['model_cache_exists']:
                        break

        return cache_info

# 自动初始化目录
Config.init_directories()
