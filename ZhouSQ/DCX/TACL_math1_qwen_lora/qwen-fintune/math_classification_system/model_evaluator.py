"""
模型评估模块 - 负责评估系统性能
"""

import json
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from datetime import datetime
from tqdm import tqdm
import logging

logger = logging.getLogger(__name__)

class ModelEvaluator:
    """模型评估器 - 负责评估系统性能"""
    
    def __init__(self, config):
        self.config = config
    
    def _load_test_data(self, test_data_path: str = None) -> Tuple[List[str], List[List[str]]]:
        """加载测试数据"""
        test_path = test_data_path or self.config.TEST_DATA_PATH
        
        try:
            with open(test_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            texts = []
            labels = []
            for item in data:
                if 'doc_token' in item and 'doc_label' in item:
                    texts.append(item['doc_token'])
                    labels.append(item['doc_label'])
            
            logger.info(f"加载测试数据: {len(texts)} 条")
            return texts, labels
            
        except Exception as e:
            logger.error(f"加载测试数据失败: {e}")
            raise
    
    def _evaluate_vector_retrieval(self, vector_builder, test_texts: List[str], test_labels: List[List[str]]) -> Dict:
        """评估向量检索性能"""
        logger.info("评估向量检索性能...")
        
        correct = 0
        total = len(test_texts)
        detailed_results = []
        
        for i, (text, true_label) in enumerate(tqdm(zip(test_texts, test_labels), desc="向量检索评估", total=total)):
            try:
                # 查找最相似的样本
                similar_samples = vector_builder.find_similar(text, top_k=1)
                
                if similar_samples:
                    predicted_label = similar_samples[0]['label']
                    similarity = similar_samples[0]['similarity']
                    is_correct = predicted_label == true_label
                    
                    if is_correct:
                        correct += 1
                    
                    detailed_results.append({
                        'text': text,
                        'true_label': true_label,
                        'predicted_label': predicted_label,
                        'similarity': similarity,
                        'is_correct': is_correct
                    })
                else:
                    detailed_results.append({
                        'text': text,
                        'true_label': true_label,
                        'predicted_label': [],
                        'similarity': 0.0,
                        'is_correct': False
                    })
            except Exception as e:
                logger.error(f"向量检索评估出错: {e}")
                detailed_results.append({
                    'text': text,
                    'true_label': true_label,
                    'predicted_label': [],
                    'similarity': 0.0,
                    'is_correct': False,
                    'error': str(e)
                })
        
        accuracy = correct / total if total > 0 else 0.0
        
        return {
            'method': 'vector_retrieval',
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'detailed_results': detailed_results
        }
    
    def _evaluate_model_classification(self, model_trainer, test_texts: List[str], test_labels: List[List[str]]) -> Dict:
        """评估模型分类性能"""
        logger.info("评估模型分类性能...")
        
        correct = 0
        total = len(test_texts)
        detailed_results = []
        
        for i, (text, true_label) in enumerate(tqdm(zip(test_texts, test_labels), desc="模型分类评估", total=total)):
            try:
                # 模型预测
                predicted_label, confidence = model_trainer.predict_single(text)
                is_correct = predicted_label == true_label
                
                if is_correct:
                    correct += 1
                
                detailed_results.append({
                    'text': text,
                    'true_label': true_label,
                    'predicted_label': predicted_label,
                    'confidence': confidence,
                    'is_correct': is_correct
                })
            except Exception as e:
                logger.error(f"模型分类评估出错: {e}")
                detailed_results.append({
                    'text': text,
                    'true_label': true_label,
                    'predicted_label': [],
                    'confidence': 0.0,
                    'is_correct': False,
                    'error': str(e)
                })
        
        accuracy = correct / total if total > 0 else 0.0
        
        return {
            'method': 'model_classification',
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'detailed_results': detailed_results
        }
    
    def _evaluate_hybrid_system(self, vector_builder, model_trainer, test_texts: List[str], test_labels: List[List[str]]) -> Dict:
        """评估混合系统性能（基于阈值的策略选择）"""
        logger.info("评估混合系统性能...")
        
        correct = 0
        total = len(test_texts)
        detailed_results = []
        strategy_usage = {'vector_retrieval': 0, 'model_classification': 0, 'failed': 0}
        
        for i, (text, true_label) in enumerate(tqdm(zip(test_texts, test_labels), desc="混合系统评估", total=total)):
            try:
                # 首先尝试向量检索
                similar_samples = vector_builder.find_similar(text, top_k=1)
                
                predicted_label = []
                confidence = 0.0
                strategy_used = 'failed'
                
                if similar_samples:
                    similarity = similar_samples[0]['similarity']
                    
                    # 根据阈值决定策略
                    if similarity >= self.config.SIMILARITY_THRESHOLD:
                        # 高相似度，使用向量检索
                        predicted_label = similar_samples[0]['label']
                        confidence = similarity
                        strategy_used = 'vector_retrieval'
                        strategy_usage['vector_retrieval'] += 1
                    else:
                        # 低相似度，使用模型分类
                        try:
                            predicted_label, model_confidence = model_trainer.predict_single(text)
                            confidence = model_confidence
                            strategy_used = 'model_classification'
                            strategy_usage['model_classification'] += 1
                        except:
                            # 模型分类失败，回退到向量检索
                            predicted_label = similar_samples[0]['label']
                            confidence = similarity
                            strategy_used = 'vector_retrieval'
                            strategy_usage['vector_retrieval'] += 1
                else:
                    # 向量检索失败，尝试模型分类
                    try:
                        predicted_label, model_confidence = model_trainer.predict_single(text)
                        confidence = model_confidence
                        strategy_used = 'model_classification'
                        strategy_usage['model_classification'] += 1
                    except:
                        strategy_usage['failed'] += 1
                
                is_correct = predicted_label == true_label
                if is_correct:
                    correct += 1
                
                detailed_results.append({
                    'text': text,
                    'true_label': true_label,
                    'predicted_label': predicted_label,
                    'confidence': confidence,
                    'strategy_used': strategy_used,
                    'is_correct': is_correct
                })
                
            except Exception as e:
                logger.error(f"混合系统评估出错: {e}")
                detailed_results.append({
                    'text': text,
                    'true_label': true_label,
                    'predicted_label': [],
                    'confidence': 0.0,
                    'strategy_used': 'failed',
                    'is_correct': False,
                    'error': str(e)
                })
                strategy_usage['failed'] += 1
        
        accuracy = correct / total if total > 0 else 0.0
        
        return {
            'method': 'hybrid_system',
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'strategy_usage': strategy_usage,
            'similarity_threshold': self.config.SIMILARITY_THRESHOLD,
            'detailed_results': detailed_results
        }
    
    def evaluate(self, vector_builder, model_trainer, test_data_path: str = None) -> Dict:
        """
        全面评估系统性能
        
        Args:
            vector_builder: 向量构建器
            model_trainer: 模型训练器
            test_data_path: 测试数据路径
            
        Returns:
            Dict: 评估结果
        """
        try:
            # 加载测试数据
            test_texts, test_labels = self._load_test_data(test_data_path)
            
            # 评估各个方法
            results = {
                'evaluation_time': datetime.now().isoformat(),
                'test_data_size': len(test_texts),
                'similarity_threshold': self.config.SIMILARITY_THRESHOLD
            }
            
            # 评估向量检索
            vector_results = self._evaluate_vector_retrieval(vector_builder, test_texts, test_labels)
            results['vector_retrieval'] = vector_results
            
            # 评估模型分类
            model_results = self._evaluate_model_classification(model_trainer, test_texts, test_labels)
            results['model_classification'] = model_results
            
            # 评估混合系统
            hybrid_results = self._evaluate_hybrid_system(vector_builder, model_trainer, test_texts, test_labels)
            results['hybrid_system'] = hybrid_results
            
            # 保存评估结果
            self._save_evaluation_results(results)
            
            logger.info("系统评估完成")
            return results
            
        except Exception as e:
            logger.error(f"系统评估失败: {e}")
            return {"error": str(e)}
    
    def _save_evaluation_results(self, results: Dict):
        """保存评估结果"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = self.config.RESULTS_DIR / f"evaluation_results_{timestamp}.json"
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"评估结果已保存: {results_file}")
            
        except Exception as e:
            logger.error(f"保存评估结果失败: {e}")
