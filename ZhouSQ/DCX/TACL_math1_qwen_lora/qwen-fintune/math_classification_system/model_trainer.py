"""
模型微调模块 - 负责LoRA微调和模型训练
"""

import json
import os
import torch
import numpy as np
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from torch.utils.data import Dataset
from transformers import (
    AutoTokenizer, AutoModelForSequenceClassification,
    Trainer, TrainingArguments, DataCollatorWithPadding
)
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score
import logging

# 禁用分布式训练相关的环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # 只使用第一个GPU
os.environ["WORLD_SIZE"] = "1"
os.environ["RANK"] = "0"
os.environ["LOCAL_RANK"] = "0"
os.environ["MASTER_ADDR"] = "localhost"
os.environ["MASTER_PORT"] = "12355"
# 完全禁用分布式训练
os.environ["TOKENIZERS_PARALLELISM"] = "false"
# 禁用DeepSpeed
os.environ["ACCELERATE_USE_DEEPSPEED"] = "false"
os.environ["ACCELERATE_USE_FSDP"] = "false"

# LoRA相关导入
try:
    from peft import LoraConfig, get_peft_model, TaskType, PeftModel
    PEFT_AVAILABLE = True
except ImportError:
    PEFT_AVAILABLE = False
    print("警告: peft库未安装，将使用全量微调")

logger = logging.getLogger(__name__)

class MathDataset(Dataset):
    """数学分类数据集"""
    
    def __init__(self, texts: List[str], labels: List[List[str]], tokenizer, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        # 将层次标签转换为字符串
        self.label_strings = [" -> ".join(label) for label in labels]
        
        # 创建标签编码器
        self.label_encoder = LabelEncoder()
        self.encoded_labels = self.label_encoder.fit_transform(self.label_strings)
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(self.encoded_labels[idx], dtype=torch.long)
        }

class ModelTrainer:
    """模型训练器 - 负责LoRA微调"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.DEVICE)
        
        # 模型组件
        self.tokenizer = None
        self.model = None
        self.label_encoder = None
        
        # 训练组件
        self.trainer = None
        
        # 模型保存路径
        self.model_save_path = config.MODELS_DIR / "classification_model"

        # 检查是否存在现有的模型
        self.existing_model_path = self._find_existing_model()

    def _find_existing_model(self) -> Optional[Path]:
        """查找现有的训练好的模型"""
        # 检查新版本模型路径
        if self.model_save_path.exists() and (self.model_save_path / "config.json").exists():
            return self.model_save_path

        # 检查旧版本模型路径 - 查找checkpoint目录
        legacy_models_dir = self.config.OUTPUT_DIR / "models"
        if legacy_models_dir.exists():
            for subdir in legacy_models_dir.iterdir():
                if subdir.is_dir() and (subdir / "adapter_config.json").exists():
                    logger.info(f"发现现有的LoRA模型: {subdir}")
                    return subdir

        return None
    
    def _load_training_data(self) -> Tuple[List[str], List[List[str]], List[str], List[List[str]]]:
        """加载训练和验证数据"""
        try:
            # 加载训练数据
            with open(self.config.TRAIN_DATA_PATH, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
            
            train_texts = []
            train_labels = []
            for item in train_data:
                if 'doc_token' in item and 'doc_label' in item:
                    train_texts.append(item['doc_token'])
                    train_labels.append(item['doc_label'])
            
            # 尝试加载验证数据
            val_texts = []
            val_labels = []
            try:
                with open(self.config.VAL_DATA_PATH, 'r', encoding='utf-8') as f:
                    val_data = json.load(f)
                
                for item in val_data:
                    if 'doc_token' in item and 'doc_label' in item:
                        val_texts.append(item['doc_token'])
                        val_labels.append(item['doc_label'])
            except:
                logger.warning("未找到验证数据，从训练数据中分割20%作为验证集")
                split_idx = int(len(train_texts) * 0.8)
                val_texts = train_texts[split_idx:]
                val_labels = train_labels[split_idx:]
                train_texts = train_texts[:split_idx]
                train_labels = train_labels[:split_idx]
            
            logger.info(f"训练数据: {len(train_texts)} 条")
            logger.info(f"验证数据: {len(val_texts)} 条")
            
            return train_texts, train_labels, val_texts, val_labels
            
        except Exception as e:
            logger.error(f"加载训练数据失败: {e}")
            raise
    
    def _load_model(self, num_labels: int):
        """加载预训练模型"""
        try:
            logger.info(f"加载分类模型: {self.config.CLASSIFICATION_MODEL_PATH}")
            
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.CLASSIFICATION_MODEL_PATH,
                trust_remote_code=True
            )
            
            # 添加pad_token如果不存在
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            # 加载模型
            self.model = AutoModelForSequenceClassification.from_pretrained(
                self.config.CLASSIFICATION_MODEL_PATH,
                num_labels=num_labels,
                trust_remote_code=True,
                pad_token_id=self.tokenizer.pad_token_id,
            )
            
            # 应用LoRA配置
            if self.config.USE_LORA and PEFT_AVAILABLE:
                logger.info("应用LoRA配置...")
                lora_config = LoraConfig(
                    task_type=TaskType.SEQ_CLS,
                    r=self.config.LORA_R,
                    lora_alpha=self.config.LORA_ALPHA,
                    lora_dropout=self.config.LORA_DROPOUT,
                    target_modules=self.config.LORA_TARGET_MODULES,
                )
                self.model = get_peft_model(self.model, lora_config)
                self.model.print_trainable_parameters()
                logger.info("LoRA配置应用成功")
            else:
                if self.config.USE_LORA:
                    logger.warning("peft库不可用，使用全量微调")
                else:
                    logger.info("使用全量微调")
            
            self.model.to(self.device)
            logger.info("分类模型加载成功")
            
        except Exception as e:
            logger.error(f"加载分类模型失败: {e}")
            raise
    
    def _prepare_datasets(self, train_texts: List[str], train_labels: List[List[str]], 
                         val_texts: List[str], val_labels: List[List[str]]) -> Tuple[MathDataset, MathDataset]:
        """准备训练数据集"""
        train_dataset = MathDataset(train_texts, train_labels, self.tokenizer, self.config.MAX_LENGTH)
        val_dataset = MathDataset(val_texts, val_labels, self.tokenizer, self.config.MAX_LENGTH)
        
        # 保存标签编码器
        self.label_encoder = train_dataset.label_encoder
        
        return train_dataset, val_dataset
    
    def _compute_metrics(self, eval_pred):
        """计算评估指标"""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        accuracy = accuracy_score(labels, predictions)
        return {'accuracy': accuracy}
    
    def _setup_trainer(self, train_dataset: MathDataset, val_dataset: MathDataset):
        """设置训练器"""
        training_args = TrainingArguments(
            output_dir=str(self.model_save_path),
            num_train_epochs=self.config.TRAIN_EPOCHS,
            per_device_train_batch_size=self.config.TRAIN_BATCH_SIZE,
            per_device_eval_batch_size=self.config.TRAIN_BATCH_SIZE,
            learning_rate=self.config.LEARNING_RATE,
            warmup_steps=100,
            weight_decay=0.01,
            logging_dir=str(self.model_save_path / 'logs'),
            logging_steps=100,
            eval_strategy="epoch",
            save_strategy="epoch",
            save_total_limit=2,
            load_best_model_at_end=True,
            metric_for_best_model="eval_accuracy",
            greater_is_better=True,
            report_to=None,  # 禁用wandb等
            remove_unused_columns=True,
            # 禁用分布式训练，强制使用单GPU
            local_rank=-1,
            ddp_backend=None,
            dataloader_num_workers=0,  # 避免多进程问题
            ddp_find_unused_parameters=False,
            use_cpu=False,  # 明确使用GPU
            no_cuda=False,  # 不禁用CUDA
            # 禁用所有分布式训练相关功能
            deepspeed=None,
            fsdp=None,
            fsdp_config=None,
        )
        
        data_collator = DataCollatorWithPadding(tokenizer=self.tokenizer)
        
        self.trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            processing_class=self.tokenizer,
            data_collator=data_collator,
            compute_metrics=self._compute_metrics,
        )
    
    def _save_model(self):
        """保存模型和标签编码器"""
        try:
            # 保存模型
            self.trainer.save_model()
            self.tokenizer.save_pretrained(self.model_save_path)
            
            # 保存标签编码器
            label_encoder_path = self.model_save_path / "label_encoder.json"
            label_mapping = {
                'classes_': self.label_encoder.classes_.tolist()
            }
            with open(label_encoder_path, 'w', encoding='utf-8') as f:
                json.dump(label_mapping, f, ensure_ascii=False, indent=2)
            
            logger.info(f"模型已保存到: {self.model_save_path}")
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            raise
    
    def train_model(self, force_retrain: bool = False) -> bool:
        """
        训练分类模型

        Args:
            force_retrain: 是否强制重新训练

        Returns:
            bool: 是否成功训练
        """
        try:
            # 检查是否已有训练好的模型
            if not force_retrain and self.existing_model_path:
                logger.info(f"发现已训练的模型: {self.existing_model_path}")
                return self.load_trained_model(str(self.existing_model_path))

            logger.info("开始训练分类模型...")
            
            # 加载训练数据
            train_texts, train_labels, val_texts, val_labels = self._load_training_data()
            
            # 获取标签数量
            all_label_strings = [" -> ".join(label) for label in train_labels + val_labels]
            num_labels = len(set(all_label_strings))
            logger.info(f"标签数量: {num_labels}")
            
            # 加载模型
            self._load_model(num_labels)
            
            # 准备数据集
            train_dataset, val_dataset = self._prepare_datasets(train_texts, train_labels, val_texts, val_labels)
            
            # 使用简单的训练循环而不是Trainer
            success = self._simple_train(train_dataset, val_dataset)

            if success:
                # 保存模型
                self._save_model_simple()
                logger.info("模型训练完成")
                return True
            else:
                return False
            
        except Exception as e:
            logger.error(f"训练模型失败: {e}")
            return False

    def _simple_train(self, train_dataset, val_dataset):
        """简单的训练循环，避免使用Trainer类"""
        try:
            from torch.utils.data import DataLoader
            from torch.optim import AdamW
            from tqdm import tqdm
            import gc

            # 清理GPU内存
            torch.cuda.empty_cache()
            gc.collect()

            # 创建数据加载器
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.config.TRAIN_BATCH_SIZE,
                shuffle=True,
                num_workers=0,  # 避免多进程问题
                pin_memory=False  # 节省内存
            )

            val_loader = DataLoader(
                val_dataset,
                batch_size=self.config.TRAIN_BATCH_SIZE,
                shuffle=False,
                num_workers=0,
                pin_memory=False
            )

            # 设置优化器
            optimizer = AdamW(self.model.parameters(), lr=self.config.LEARNING_RATE)

            # 梯度累积步数
            accumulation_steps = 4

            # 训练循环
            self.model.train()
            for epoch in range(self.config.TRAIN_EPOCHS):
                total_loss = 0
                progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{self.config.TRAIN_EPOCHS}")

                optimizer.zero_grad()

                for step, batch in enumerate(progress_bar):
                    # 移动数据到设备
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = batch['labels'].to(self.device)

                    # 前向传播
                    outputs = self.model(
                        input_ids=input_ids,
                        attention_mask=attention_mask,
                        labels=labels
                    )

                    loss = outputs.loss / accumulation_steps  # 缩放损失
                    total_loss += loss.item() * accumulation_steps

                    # 反向传播
                    loss.backward()

                    # 梯度累积
                    if (step + 1) % accumulation_steps == 0:
                        optimizer.step()
                        optimizer.zero_grad()

                        # 清理GPU内存
                        torch.cuda.empty_cache()

                    # 更新进度条
                    progress_bar.set_postfix({'loss': f'{loss.item() * accumulation_steps:.4f}'})

                # 处理剩余的梯度
                if len(train_loader) % accumulation_steps != 0:
                    optimizer.step()
                    optimizer.zero_grad()

                avg_loss = total_loss / len(train_loader)
                logger.info(f"Epoch {epoch+1} 平均损失: {avg_loss:.4f}")

                # 验证
                if epoch % 2 == 0:  # 每两个epoch验证一次
                    val_acc = self._validate(val_loader)
                    logger.info(f"Epoch {epoch+1} 验证准确率: {val_acc:.4f}")

                # 清理内存
                torch.cuda.empty_cache()
                gc.collect()

            return True

        except Exception as e:
            logger.error(f"简单训练失败: {e}")
            return False

    def _validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        correct = 0
        total = 0

        with torch.no_grad():
            for batch in val_loader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)

                outputs = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )

                predictions = torch.argmax(outputs.logits, dim=-1)
                correct += (predictions == labels).sum().item()
                total += labels.size(0)

        self.model.train()
        return correct / total if total > 0 else 0.0

    def _save_model_simple(self):
        """简单的模型保存方法"""
        try:
            # 确保目录存在
            self.model_save_path.mkdir(parents=True, exist_ok=True)

            # 保存模型
            self.model.save_pretrained(self.model_save_path)
            self.tokenizer.save_pretrained(self.model_save_path)

            # 保存标签编码器
            label_encoder_path = self.model_save_path / "label_encoder.json"
            label_mapping = {
                str(i): label for i, label in enumerate(self.label_encoder.classes_)
            }
            with open(label_encoder_path, 'w', encoding='utf-8') as f:
                json.dump(label_mapping, f, ensure_ascii=False, indent=2)

            logger.info(f"模型已保存到: {self.model_save_path}")

        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            raise

    def load_trained_model(self, model_path: str = None) -> bool:
        """加载已训练的模型"""
        model_path = model_path or str(self.model_save_path)

        try:
            logger.info(f"加载已训练的模型: {model_path}")

            # 检查是否有tokenizer文件，如果没有则使用基础模型的tokenizer
            tokenizer_path = model_path
            if not (Path(model_path) / "tokenizer_config.json").exists():
                logger.info("模型目录中没有tokenizer，使用基础模型的tokenizer")
                tokenizer_path = self.config.CLASSIFICATION_MODEL_PATH

            self.tokenizer = AutoTokenizer.from_pretrained(
                tokenizer_path,
                trust_remote_code=True
            )

            # 检查是否是LoRA模型
            adapter_config_path = Path(model_path) / "adapter_config.json"
            if adapter_config_path.exists() and PEFT_AVAILABLE:
                logger.info("检测到LoRA模型，正在加载...")

                # 需要先确定标签数量来加载基础模型
                try:
                    # 尝试从adapter_config.json获取标签数量
                    import json
                    with open(adapter_config_path, 'r') as f:
                        adapter_config = json.load(f)

                    # 如果有保存的标签编码器，从中获取标签数量
                    label_encoder_path = Path(model_path) / "label_encoder.json"
                    if label_encoder_path.exists():
                        with open(label_encoder_path, 'r', encoding='utf-8') as f:
                            label_mapping = json.load(f)
                        num_labels = len(label_mapping['classes_'])
                    else:
                        # 回退：从训练数据推断标签数量
                        logger.warning("未找到标签编码器，从训练数据推断标签数量")
                        _, train_labels, _, _ = self._load_training_data()
                        all_label_strings = [" -> ".join(label) for label in train_labels]
                        num_labels = len(set(all_label_strings))

                    logger.info(f"推断标签数量: {num_labels}")

                    # 先加载基础模型
                    base_model = AutoModelForSequenceClassification.from_pretrained(
                        self.config.CLASSIFICATION_MODEL_PATH,
                        num_labels=num_labels,
                        trust_remote_code=True
                    )
                    # 加载LoRA适配器
                    self.model = PeftModel.from_pretrained(base_model, model_path)
                    logger.info("LoRA模型加载成功")

                except Exception as e:
                    logger.error(f"加载LoRA模型失败: {e}")
                    raise
            else:
                # 加载全量微调模型
                self.model = AutoModelForSequenceClassification.from_pretrained(
                    model_path,
                    trust_remote_code=True
                )
                logger.info("全量微调模型加载成功")

            self.model.to(self.device)
            self.model.eval()

            # 加载标签编码器
            label_encoder_path = Path(model_path) / "label_encoder.json"
            if label_encoder_path.exists():
                with open(label_encoder_path, 'r', encoding='utf-8') as f:
                    label_mapping = json.load(f)

                from sklearn.preprocessing import LabelEncoder
                self.label_encoder = LabelEncoder()
                self.label_encoder.classes_ = np.array(label_mapping['classes_'])
            else:
                # 如果没有保存的标签编码器，从训练数据重新创建
                logger.warning("未找到保存的标签编码器，从训练数据重新创建")
                _, train_labels, _, _ = self._load_training_data()
                label_strings = [" -> ".join(label) for label in train_labels]

                from sklearn.preprocessing import LabelEncoder
                self.label_encoder = LabelEncoder()
                self.label_encoder.fit(label_strings)

            logger.info("已训练模型加载成功")
            return True

        except Exception as e:
            logger.error(f"加载已训练模型失败: {e}")
            return False

    def predict(self, texts: List[str]) -> List[Tuple[List[str], float]]:
        """预测文本标签"""
        if self.model is None or self.label_encoder is None:
            raise ValueError("模型未加载，请先训练或加载模型")

        self.model.eval()
        predictions = []

        with torch.no_grad():
            for text in texts:
                # 编码文本
                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.config.MAX_LENGTH,
                    return_tensors='pt'
                ).to(self.device)

                # 预测
                outputs = self.model(**encoding)
                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=-1)

                # 获取最高概率的预测
                predicted_class_id = torch.argmax(probabilities, dim=-1).item()
                confidence = probabilities[0][predicted_class_id].item()

                # 解码标签
                predicted_label_string = self.label_encoder.inverse_transform([predicted_class_id])[0]
                predicted_label = predicted_label_string.split(" -> ")

                predictions.append((predicted_label, confidence))

        return predictions

    def predict_single(self, text: str) -> Tuple[List[str], float]:
        """预测单个文本"""
        results = self.predict([text])
        return results[0] if results else ([], 0.0)
